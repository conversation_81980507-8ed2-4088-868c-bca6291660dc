// DeepgramService.ts
import { createClient } from '@deepgram/sdk';
import { EventEmitter } from 'events';
import { BrowserWindow } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { exec } from 'child_process';

/**
 * Service for handling speech-to-text using Deepgram
 */
export class DeepgramService extends EventEmitter {
  private apiKey: string;
  private deepgram: ReturnType<typeof createClient>;
  private mainWindow: BrowserWindow | null;
  private isReady: boolean = false;
  private tempDir: string;

  constructor(apiKey: string, mainWindow: BrowserWindow | null = null) {
    super();
    this.apiKey = apiKey;
    this.mainWindow = mainWindow;
    this.tempDir = path.join(os.tmpdir(), 'code-genius-audio');

    // Create temp directory if it doesn't exist
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }

    // Initialize Deepgram client
    try {
      this.deepgram = createClient(this.apiKey);
      this.isReady = true;
      console.log('Deepgram service initialized successfully');
      console.log('Deepgram API key (first 4 chars):', this.apiKey.substring(0, 4));

      // Test the API key with a simple request
      this.testApiKey().then(isValid => {
        console.log('Deepgram API key test result:', isValid ? 'Valid' : 'Invalid');
        if (!isValid) {
          console.error('Deepgram API key validation failed. Speech-to-text may not work correctly.');
          if (this.mainWindow) {
            this.mainWindow.webContents.send('deepgram-api-error', {
              error: 'Deepgram API key validation failed',
              details: 'Please check your API key in the .env file'
            });
          }
        }
      }).catch(error => {
        console.error('Error testing Deepgram API key:', error);
      });
    } catch (error) {
      console.error('Failed to initialize Deepgram service:', error);
      this.isReady = false;
    }
  }

  /**
   * Test the Deepgram API key by making a simple request
   * @returns Promise resolving to true if the API key is valid, false otherwise
   */
  private async testApiKey(): Promise<boolean> {
    try {
      console.log('Testing Deepgram API key...');

      // Try to use a sample WAV file if available
      const sampleWavPath = path.join(__dirname, '..', 'sample-audio.wav');

      if (fs.existsSync(sampleWavPath)) {
        console.log('Using sample WAV file for API key test:', sampleWavPath);
        const sampleData = fs.readFileSync(sampleWavPath);

        try {
          const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
            sampleData,
            {
              model: 'general',
              language: 'en',
              smart_format: true,
              mimetype: 'audio/wav',
            },
          );

          if (error) {
            console.error('Deepgram API test error with sample WAV:', error);
            return false;
          }

          const transcript = result?.results?.channels[0]?.alternatives[0]?.transcript || '';
          console.log('Sample WAV transcription result:', transcript);

          // If we got any result, the API key is working
          return true;
        } catch (sampleError) {
          console.error('Error testing with sample WAV:', sampleError);
          // Continue with fallback test
        }
      } else {
        console.log('No sample WAV file found, using minimal test buffer');
      }

      // Fallback: Create a simple test audio buffer (silence)
      const testBuffer = Buffer.alloc(44 + 1000); // WAV header (44 bytes) + 1000 bytes of silence

      // Write WAV header
      testBuffer.write('RIFF', 0);
      testBuffer.writeUInt32LE(testBuffer.length - 8, 4);
      testBuffer.write('WAVE', 8);
      testBuffer.write('fmt ', 12);
      testBuffer.writeUInt32LE(16, 16);
      testBuffer.writeUInt16LE(1, 20); // PCM format
      testBuffer.writeUInt16LE(1, 22); // 1 channel
      testBuffer.writeUInt32LE(44100, 24); // 44.1kHz
      testBuffer.writeUInt32LE(44100 * 2, 28); // Byte rate
      testBuffer.writeUInt16LE(2, 32); // Block align
      testBuffer.writeUInt16LE(16, 34); // 16 bits per sample
      testBuffer.write('data', 36);
      testBuffer.writeUInt32LE(testBuffer.length - 44, 40);

      // Try to use the API with minimal data
      try {
        const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
          testBuffer,
          {
            model: 'general',
            language: 'en',
            smart_format: true,
            mimetype: 'audio/wav',
          },
        );

        if (error) {
          console.error('Deepgram API test error with fallback buffer:', error);
          return false;
        }

        // If we get here without an error, the API key is likely valid
        console.log('Deepgram API test successful with fallback buffer');
        return true;
      } catch (fallbackError) {
        console.error('Error testing with fallback buffer:', fallbackError);
        return false;
      }
    } catch (error) {
      console.error('Error testing Deepgram API key:', error);
      return false;
    }
  }

  /**
   * Check if the Deepgram service is ready
   * @returns True if the service is ready, false otherwise
   */
  public ready(): boolean {
    return this.isReady;
  }

  /**
   * Convert WebM audio to WAV format using ffmpeg
   * @param webmBuffer WebM audio buffer
   * @returns Promise resolving to WAV audio buffer
   */
  private async convertWebmToWav(webmBuffer: Buffer): Promise<Buffer> {
    try {
      // Create temporary files
      const tempWebmPath = path.join(this.tempDir, `temp_${Date.now()}.webm`);
      const tempWavPath = path.join(this.tempDir, `temp_${Date.now()}.wav`);

      // Write WebM buffer to temporary file
      fs.writeFileSync(tempWebmPath, webmBuffer);
      console.log(`Saved WebM buffer to temporary file: ${tempWebmPath}`);

      // Convert WebM to WAV using ffmpeg
      return new Promise((resolve, reject) => {
        const command = `ffmpeg -i "${tempWebmPath}" -acodec pcm_s16le -ar 44100 -ac 1 "${tempWavPath}"`;
        console.log(`Executing ffmpeg command: ${command}`);

        exec(command, (error, stdout, stderr) => {
          // Clean up WebM file
          try {
            fs.unlinkSync(tempWebmPath);
            console.log(`Deleted temporary WebM file: ${tempWebmPath}`);
          } catch (cleanupError) {
            console.error(`Error deleting temporary WebM file: ${cleanupError}`);
          }

          if (error) {
            console.error(`Error executing ffmpeg: ${error.message}`);
            reject(error);
            return;
          }

          // Read WAV file
          try {
            const wavBuffer = fs.readFileSync(tempWavPath);
            console.log(`Read WAV file: ${tempWavPath}, size: ${wavBuffer.length}`);

            // Clean up WAV file
            try {
              fs.unlinkSync(tempWavPath);
              console.log(`Deleted temporary WAV file: ${tempWavPath}`);
            } catch (cleanupError) {
              console.error(`Error deleting temporary WAV file: ${cleanupError}`);
            }

            resolve(wavBuffer);
          } catch (readError) {
            console.error(`Error reading WAV file: ${readError}`);
            reject(readError);
          }
        });
      });
    } catch (error) {
      console.error(`Error converting WebM to WAV: ${error}`);
      throw error;
    }
  }

  /**
   * Transcribe audio from a file
   * @param audioFilePath Path to the audio file
   * @returns Transcription result
   */
  public async transcribeFile(audioFilePath: string): Promise<string> {
    try {
      if (!this.isReady) {
        throw new Error('Deepgram service is not ready');
      }

      console.log(`Transcribing audio file: ${audioFilePath}`);

      // Read the audio file
      const audioData = await fs.promises.readFile(audioFilePath);

      // Transcribe the audio
      const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
        audioData,
        {
          model: 'general',
          language: 'en',
          smart_format: true,
        },
      );

      if (error) {
        console.error('Error transcribing audio:', error);
        throw error;
      }

      // Extract the transcript from the result
      const transcript = result.results?.channels[0]?.alternatives[0]?.transcript || '';
      console.log('Transcription result:', transcript);

      return transcript;
    } catch (error) {
      console.error('Error transcribing audio file:', error);
      throw error;
    }
  }

  /**
   * Transcribe audio from a URL
   * @param audioUrl URL of the audio file
   * @returns Transcription result
   */
  public async transcribeUrl(audioUrl: string): Promise<string> {
    try {
      if (!this.isReady) {
        throw new Error('Deepgram service is not ready');
      }

      console.log(`Transcribing audio from URL: ${audioUrl}`);

      // Transcribe the audio from URL
      const { result, error } = await this.deepgram.listen.prerecorded.transcribeUrl(
        { url: audioUrl },
        {
          model: 'general',
          language: 'en',
          smart_format: true,
        },
      );

      if (error) {
        console.error('Error transcribing audio from URL:', error);
        throw error;
      }

      // Extract the transcript from the result
      const transcript = result.results?.channels[0]?.alternatives[0]?.transcript || '';
      console.log('Transcription result:', transcript);

      return transcript;
    } catch (error) {
      console.error('Error transcribing audio from URL:', error);
      throw error;
    }
  }

  /**
   * Save audio data to a temporary file and transcribe it
   * @param audioData Audio data as a Buffer
   * @param isUserSpeech Whether this is user speech (true) or system audio (false)
   * @returns Transcription result
   */
  public async transcribeBuffer(audioData: Buffer, isUserSpeech: boolean = true): Promise<string> {
    try {
      if (!this.isReady) {
        throw new Error('Deepgram service is not ready');
      }

      console.log('Transcribing audio from buffer, size:', audioData.length);
      console.log(`Audio source: ${isUserSpeech ? 'User speaking' : 'System audio'}`);

      // Check if buffer is too small
      if (audioData.length < 5000) {
        console.log('Audio buffer too small, might not contain enough audio data');
      }

      // For debugging purposes, let's try a known good sample file if available
      // Try WAV file first
      const sampleWavPath = path.join(__dirname, '..', 'sample-audio.wav');
      const sampleWebmPath = path.join(__dirname, '..', 'sample-audio.webm');

      // Check for WAV file first, then WebM
      const samplePath = fs.existsSync(sampleWavPath) ? sampleWavPath :
                         fs.existsSync(sampleWebmPath) ? sampleWebmPath : null;

      if (samplePath) {
        try {
          console.log('Found sample audio file, trying to transcribe it as a test:', samplePath);
          const sampleData = fs.readFileSync(samplePath);

          // Determine MIME type based on file extension
          const mimeType = samplePath.endsWith('.wav') ? 'audio/wav' : 'audio/webm';
          console.log('Using MIME type for sample file:', mimeType);

          console.log('Attempting to transcribe sample file with Deepgram...');

          // Try with different models to see which one works
          // Order models by likelihood of availability with free/basic API keys
          // Use general model which is available on free tier as suggested by error message
          // Avoid nova-3, nova-2, and enhanced which require higher permissions
          const models = ['general'];
          let transcriptionResult = null;
          let lastError = null;

          for (const model of models) {
            try {
              console.log(`Trying sample transcription with model: ${model}`);
              const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
                sampleData,
                {
                  model: model,
                  language: 'en',
                  smart_format: true,
                  mimetype: mimeType,
                  detect_language: true,
                  diarize: false,
                  punctuate: true,
                  utterances: true,
                  profanity_filter: false,
                  tier: 'base', // Use base tier for free/basic accounts
                },
              );

              if (error) {
                console.error(`Error with model ${model}:`, error);

                // Check for permission errors and log more helpful messages
                if (typeof error === 'object' && error !== null && 'err_code' in error && error.err_code === 'INSUFFICIENT_PERMISSIONS') {
                  console.error(`Your account doesn't have access to the ${model} model. Skipping and trying next model.`);
                }

                lastError = error;
                continue;
              }

              const transcript = result?.results?.channels[0]?.alternatives[0]?.transcript || '';
              if (transcript) {
                console.log(`Successful sample transcription with model ${model}:`, transcript);
                transcriptionResult = result;
                break;
              } else {
                console.log(`Model ${model} returned empty transcript for sample`);
              }
            } catch (modelError) {
              console.error(`Exception with model ${model}:`, modelError);
              lastError = modelError;
            }
          }

          // Use the result from the successful model, or the last error
          const result = transcriptionResult || { results: { channels: [{ alternatives: [{ transcript: '' }] }] } };

          const sampleTranscript = result?.results?.channels[0]?.alternatives[0]?.transcript || '';
          console.log('Sample audio transcription result:', sampleTranscript);
          console.log('Sample transcription successful:', !!sampleTranscript);

          if (!sampleTranscript) {
            console.log('Sample transcription failed, there may be an issue with the Deepgram API');
          } else {
            console.log('Sample transcription succeeded, the Deepgram API is working correctly');
            console.log('The issue may be with the format of the captured audio');
          }
        } catch (sampleError) {
          console.error('Error transcribing sample audio:', sampleError);
          console.error('Sample error details:', sampleError.message);
          if (sampleError.stack) {
            console.error('Sample error stack:', sampleError.stack);
          }
        }
      } else {
        console.log('No sample audio files found. Checked:');
        console.log('- WAV:', sampleWavPath);
        console.log('- WebM:', sampleWebmPath);
      }

      // Log the first few bytes to help debug format issues
      if (audioData.length > 20) {
        console.log('Audio buffer first 20 bytes:',
          Array.from(audioData.slice(0, 20))
            .map(b => b.toString(16).padStart(2, '0'))
            .join(' ')
        );
      }

      // Detect audio format and try direct transcription with appropriate format
      try {
        // Check if the audio data starts with the WebM header (0x1A 0x45 0xDF 0xA3)
        const isWebM = audioData.length > 4 &&
                       audioData[0] === 0x1A &&
                       audioData[1] === 0x45 &&
                       audioData[2] === 0xDF &&
                       audioData[3] === 0xA3;

        console.log('Audio data appears to be WebM format:', isWebM);

        // Use the correct MIME type based on detected format
        const detectedMimeType = isWebM ? 'audio/webm' : 'audio/wav';
        console.log(`Using detected format: ${detectedMimeType} for direct transcription`);

        // Try transcription with detected format first
        console.log(`Attempting direct transcription with ${detectedMimeType}...`);

        // Try with different models to see which one works
        // Use general model which is available on free tier as suggested by error message
        // Avoid nova-3, nova-2, and enhanced which require higher permissions
        const models = ['general'];
        let transcriptionResult = null;
        let lastError = null;

        // Optimize settings based on audio source
        const transcriptionSettings = {
          model: 'general', // Will be overridden in the loop, but default to general
          language: 'en',
          smart_format: true,
          punctuate: true,
          mimetype: detectedMimeType,
          detect_language: true,
          profanity_filter: false,
          // Add specific settings based on audio source
          ...(isUserSpeech ? {
            // Settings optimized for user speech
            diarize: false,
            utterances: true,
            model_boost: 'interview', // Boost for conversational speech
            tier: 'base', // Use base tier for free/basic accounts
            keywords: ['interview', 'job', 'experience', 'skills', 'project', 'education', 'background', 'position', 'company', 'team', 'work']
          } : {
            // Settings optimized for system audio
            diarize: false,
            utterances: false,
            model_boost: 'lecture', // Boost for more formal speech
            tier: 'base', // Use base tier for free/basic accounts
            filler_words: false, // Remove filler words for cleaner transcription
            endpointing: 0, // Disable endpointing for continuous audio
            keywords: ['interview', 'job', 'experience', 'skills', 'project', 'education', 'background', 'position', 'company', 'team', 'work']
          })
        };

        for (const model of models) {
          try {
            console.log(`Trying direct transcription with model: ${model}`);
            // Update the model in the settings
            transcriptionSettings.model = model;

            const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
              audioData,
              transcriptionSettings,
            );

            if (error) {
              console.error(`Error with model ${model}:`, error);

              // Check for permission errors and log more helpful messages
              if (typeof error === 'object' && error !== null && 'err_code' in error && error.err_code === 'INSUFFICIENT_PERMISSIONS') {
                console.error(`Your account doesn't have access to the ${model} model. Skipping and trying next model.`);
              }

              lastError = error;
              continue;
            }

            const transcript = result?.results?.channels[0]?.alternatives[0]?.transcript || '';
            if (transcript) {
              console.log(`Successful direct transcription with model ${model}:`, transcript);
              transcriptionResult = result;
              break;
            } else {
              console.log(`Model ${model} returned empty transcript for direct transcription`);
            }
          } catch (modelError) {
            console.error(`Exception with model ${model}:`, modelError);
            lastError = modelError;
          }
        }

        // Use the result from the successful model, or create an empty result
        const result = transcriptionResult || { results: { channels: [{ alternatives: [{ transcript: '' }] }] } };

        const transcript = result?.results?.channels[0]?.alternatives[0]?.transcript || '';
        if (transcript) {
          console.log(`Direct transcription successful with ${detectedMimeType}:`, transcript);
          return transcript;
        } else {
          console.log(`Direct transcription with ${detectedMimeType} returned empty result, trying file-based approach...`);
        }
      } catch (error) {
        console.log('Direct transcription failed, trying file-based approach...', error);
      }

      // Check if the audio data starts with the WebM header (if we haven't already)
      const isWebM = audioData.length > 4 &&
                     audioData[0] === 0x1A &&
                     audioData[1] === 0x45 &&
                     audioData[2] === 0xDF &&
                     audioData[3] === 0xA3;

      // Use the correct file extension and MIME type based on detected format
      const fileExtension = isWebM ? 'webm' : 'wav';
      const mimeType = isWebM ? 'audio/webm' : 'audio/wav';

      // Create a temporary file with the appropriate extension
      const tempFilePath = path.join(this.tempDir, `audio_${Date.now()}.${fileExtension}`);

      console.log(`Using file extension: ${fileExtension} and MIME type: ${mimeType} for file-based approach`);

      try {
        // Save the buffer to a file with the appropriate extension
        fs.writeFileSync(tempFilePath, audioData);
        console.log(`Audio saved to temporary file: ${tempFilePath}`);

        // Read the file as a proper audio file
        const fileData = fs.readFileSync(tempFilePath);

        // Transcribe the audio from the file with the correct MIME type
        console.log(`Attempting file-based transcription with ${mimeType}...`);

        // Try with different models to see which one works
        // Use general model which is available on free tier as suggested by error message
        // Avoid nova-3, nova-2, and enhanced which require higher permissions
        const models = ['general'];
        let transcriptionResult = null;
        let transcriptionError = null;

        // Optimize settings based on audio source
        const transcriptionSettings = {
          model: 'general', // Will be overridden in the loop, but default to general
          language: 'en',
          smart_format: true,
          punctuate: true,
          mimetype: mimeType, // Use the detected MIME type
          detect_language: true,
          profanity_filter: false,
          // Add specific settings based on audio source
          ...(isUserSpeech ? {
            // Settings optimized for user speech
            diarize: false,
            utterances: true,
            model_boost: 'interview', // Boost for conversational speech
            tier: 'base', // Use base tier for free/basic accounts
            keywords: ['interview', 'job', 'experience', 'skills', 'project', 'education', 'background', 'position', 'company', 'team', 'work']
          } : {
            // Settings optimized for system audio
            diarize: false,
            utterances: false,
            model_boost: 'lecture', // Boost for more formal speech
            tier: 'base', // Use base tier for free/basic accounts
            filler_words: false, // Remove filler words for cleaner transcription
            endpointing: 0, // Disable endpointing for continuous audio
            keywords: ['interview', 'job', 'experience', 'skills', 'project', 'education', 'background', 'position', 'company', 'team', 'work']
          })
        };

        for (const model of models) {
          try {
            console.log(`Trying file-based transcription with model: ${model}`);
            // Update the model in the settings
            transcriptionSettings.model = model;

            const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
              fileData,
              transcriptionSettings,
            );

            if (error) {
              console.error(`Error with model ${model}:`, error);

              // Check for permission errors and log more helpful messages
              if (typeof error === 'object' && error !== null && 'err_code' in error && error.err_code === 'INSUFFICIENT_PERMISSIONS') {
                console.error(`Your account doesn't have access to the ${model} model. Skipping and trying next model.`);
              }

              transcriptionError = error;
              continue;
            }

            const transcript = result?.results?.channels[0]?.alternatives[0]?.transcript || '';
            if (transcript) {
              console.log(`Successful file-based transcription with model ${model}:`, transcript);
              transcriptionResult = result;
              break;
            } else {
              console.log(`Model ${model} returned empty transcript for file-based transcription`);
            }
          } catch (modelError) {
            console.error(`Exception with model ${model}:`, modelError);
            transcriptionError = modelError;
          }
        }

        // Use the result from the successful model, or the error
        const result = transcriptionResult || { results: { channels: [{ alternatives: [{ transcript: '' }] }] } };
        const error = transcriptionResult ? null : transcriptionError;

        if (error) {
          console.error('Error transcribing audio from file:', error);
          throw error;
        }

        // Extract the transcript from the result
        const transcript = result.results?.channels[0]?.alternatives[0]?.transcript || '';
        console.log('Transcription result:', transcript);

        // Clean up the temporary file
        try {
          fs.unlinkSync(tempFilePath);
          console.log(`Temporary file deleted: ${tempFilePath}`);
        } catch (cleanupError) {
          console.error('Error deleting temporary file:', cleanupError);
        }

        return transcript;
      } catch (fileError) {
        console.error('Error processing audio file:', fileError);

        // Check again if the audio data is in WebM format
        const isWebMFormat = audioData.length > 4 &&
                           audioData[0] === 0x1A &&
                           audioData[1] === 0x45 &&
                           audioData[2] === 0xDF &&
                           audioData[3] === 0xA3;

        // Try converting WebM to WAV if the audio is in WebM format
        if (isWebMFormat) {
          console.log('Trying to convert WebM to WAV format...');
          try {
            // Try to convert WebM to WAV
            const wavBuffer = await this.convertWebmToWav(audioData);
            console.log(`Successfully converted WebM to WAV, new size: ${wavBuffer.length}`);

            // Try transcribing the WAV buffer
            console.log('Transcribing converted WAV buffer...');
            const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
              wavBuffer,
              {
                model: isUserSpeech ? 'general' : 'meeting', // Use meeting model for system audio
                language: 'en',
                smart_format: true,
                punctuate: true,
                mimetype: 'audio/wav',
                tier: 'enhanced', // Use enhanced tier for better accuracy
                profanity_filter: false,
                diarize: !isUserSpeech, // Enable speaker diarization for system audio
                multichannel: !isUserSpeech, // Enable multichannel for system audio
                alternatives: 3, // Get multiple alternatives for better accuracy
                keywords: ['interview', 'job', 'experience', 'skills', 'project', 'education', 'background', 'position', 'company', 'team', 'work', 'coding', 'programming', 'algorithm', 'data', 'structure', 'function', 'variable', 'class', 'method'],
                search: ['question', 'answer', 'explain', 'describe', 'tell me', 'what is', 'how do', 'why', 'when', 'where']
              },
            );

            if (error) {
              console.error('Error transcribing converted WAV:', error);
            } else {
              const transcript = result?.results?.channels[0]?.alternatives[0]?.transcript || '';
              if (transcript) {
                console.log('Successfully transcribed converted WAV:', transcript);
                return transcript;
              } else {
                console.log('Converted WAV transcription returned empty result');
              }
            }
          } catch (conversionError) {
            console.error('Error converting WebM to WAV:', conversionError);
            console.log('Continuing with fallback transcription methods...');
          }
        }

        // Try direct buffer transcription as fallback
        console.log('Attempting direct buffer transcription as fallback...');

        // Try multiple MIME types as fallback, but prioritize the detected format
        let transcriptionResult = null;
        let transcriptionError = null;

        // Check if the audio data starts with the WebM header (0x1A 0x45 0xDF 0xA3) if we haven't already
        const isWebM = audioData.length > 4 &&
                       audioData[0] === 0x1A &&
                       audioData[1] === 0x45 &&
                       audioData[2] === 0xDF &&
                       audioData[3] === 0xA3;

        console.log('Audio data appears to be WebM format:', isWebM);

        // Create an ordered list of MIME types to try, with the detected format first
        const primaryMimeType = isWebM ? 'audio/webm' : 'audio/wav';
        const secondaryMimeType = isWebM ? 'audio/webm;codecs=opus' : 'audio/pcm';

        // Order MIME types with the detected format first, then others
        const orderedMimeTypes = [
          primaryMimeType,
          secondaryMimeType,
          'audio/wav',
          'audio/webm',
          'audio/webm;codecs=opus',
          'audio/ogg',
          'audio/ogg;codecs=opus',
          'audio/mp4',
          'audio/mp3'
        ].filter((value, index, self) => self.indexOf(value) === index); // Remove duplicates

        console.log('Trying MIME types in this order:', orderedMimeTypes.join(', '));

        // Try each MIME type until one works
        for (const mimeType of orderedMimeTypes) {
          try {
            console.log(`Trying transcription with MIME type: ${mimeType}`);
            const { result, error } = await this.deepgram.listen.prerecorded.transcribeFile(
              audioData,
              {
                model: isUserSpeech ? 'general' : 'meeting', // Use meeting model for system audio
                language: 'en',
                smart_format: true,
                punctuate: true,
                mimetype: mimeType,
                tier: 'enhanced', // Use enhanced tier for better accuracy
                profanity_filter: false,
                diarize: !isUserSpeech, // Enable speaker diarization for system audio
                multichannel: !isUserSpeech, // Enable multichannel for system audio
                alternatives: 3, // Get multiple alternatives for better accuracy
                keywords: ['interview', 'job', 'experience', 'skills', 'project', 'education', 'background', 'position', 'company', 'team', 'work', 'coding', 'programming', 'algorithm', 'data', 'structure', 'function', 'variable', 'class', 'method'],
                search: ['question', 'answer', 'explain', 'describe', 'tell me', 'what is', 'how do', 'why', 'when', 'where']
              },
            );

            if (error) {
              console.error(`Error with MIME type ${mimeType}:`, error);
              transcriptionError = error;
              continue;
            }

            // If we got a result with a transcript, use it
            if (result && result.results?.channels[0]?.alternatives[0]?.transcript) {
              transcriptionResult = result;
              console.log(`Successful transcription with MIME type: ${mimeType}`);
              break;
            }
          } catch (err) {
            console.error(`Exception with MIME type ${mimeType}:`, err);
            continue;
          }
        }

        // Use the result from the successful MIME type, or throw the last error
        if (transcriptionResult) {
          // Extract the transcript from the result
          const transcript = transcriptionResult.results?.channels[0]?.alternatives[0]?.transcript || '';
          console.log('Fallback transcription result:', transcript);
          return transcript;
        } else if (transcriptionError) {
          console.error('Error in all fallback transcription attempts:', transcriptionError);
          throw transcriptionError;
        } else {
          console.error('No successful transcription and no specific error');
          throw new Error('Failed to transcribe audio with any MIME type');
        }
      }
    } catch (error) {
      console.error('Error transcribing audio from buffer:', error);

      // Log more detailed error information
      if (error instanceof Error) {
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);

        // Check if it's an API error with more details
        if ('response' in error && error.response) {
          console.error('API response:', error.response);
        }
      }

      throw error;
    }
  }
}
