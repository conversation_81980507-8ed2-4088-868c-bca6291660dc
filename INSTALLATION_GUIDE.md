# Code Genius - Installation Guide

## 📦 Executable Files Created

Your **Code Genius - AI-Powered Interview Assistant** has been successfully built! Here's what was created:

### 🎯 Ready-to-Run Executable
- **Location**: `packaged-app/Code Genius-win32-x64/`
- **Executable**: `Code Genius.exe`
- **Platform**: Windows x64
- **Type**: Portable (no installation required)

## 🚀 How to Run the Application

### Option 1: Direct Execution
1. Navigate to: `packaged-app/Code Genius-win32-x64/`
2. Double-click on `Code Genius.exe`
3. The application will start immediately

### Option 2: Create Desktop Shortcut
1. Right-click on `Code Genius.exe`
2. Select "Create shortcut"
3. Move the shortcut to your Desktop
4. Rename it to "Code Genius"

## ⚙️ First-Time Setup

### 1. Environment Configuration
Before running the application, you need to set up your API keys:

1. Create a `.env` file in the same directory as `Code Genius.exe`
2. Add your API keys:

```env
# Gemini API Keys
GEMINI_API_KEY="your_gemini_api_key_here"
GEMINI_LIVE_API_KEY="your_gemini_api_key_here"

# Deepgram API Key (optional but recommended)
DEEPGRAM_API_KEY="your_deepgram_api_key_here"

# Supabase Configuration (optional)
VITE_SUPABASE_URL="https://your-project.supabase.co"
VITE_SUPABASE_ANON_KEY="your_supabase_anon_key"

# Environment
NODE_ENV=production
```

### 2. Get API Keys

#### Gemini API Key (Required)
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key to your `.env` file

#### Deepgram API Key (Optional but Recommended)
1. Visit [Deepgram Console](https://console.deepgram.com/)
2. Create a free account (includes free credits)
3. Generate an API key
4. Copy the key to your `.env` file

## 🎮 Using the Application

### Basic Features
- **Voice Input**: Click the microphone button and speak
- **Text Input**: Type questions in the chat interface
- **AI Responses**: Get intelligent answers for coding questions
- **Interview Mode**: Practice technical interviews

### Advanced Features
- **Autonomous Mode**: Automatically detect and process audio
- **System Audio Monitoring**: Capture interviewer questions
- **Context Awareness**: Maintains conversation history
- **Multi-language Support**: Support for various programming languages

## 📁 File Structure

```
Code Genius-win32-x64/
├── Code Genius.exe          # Main executable
├── resources/               # Application resources
├── locales/                 # Language files
├── *.dll                    # Required libraries
├── *.pak                    # Chromium resources
└── .env                     # Your API keys (create this)
```

## 🔧 Troubleshooting

### Application Won't Start
1. **Check Windows Defender**: Add the folder to exclusions
2. **Missing .env file**: Ensure you've created the `.env` file with API keys
3. **Antivirus blocking**: Temporarily disable antivirus and try again

### API Key Issues
1. **Invalid key**: Verify your Gemini API key is correct
2. **Quota exceeded**: Check your API usage limits
3. **Network issues**: Ensure stable internet connection

### Audio Problems
1. **Microphone permissions**: Allow microphone access when prompted
2. **No audio detected**: Check microphone settings in Windows
3. **Deepgram issues**: Verify Deepgram API key or use browser fallback

## 🛡️ Security Notes

- **API Keys**: Keep your `.env` file secure and never share it
- **Firewall**: Windows may ask for firewall permissions - allow them
- **Antivirus**: Some antivirus software may flag Electron apps as suspicious

## 📋 System Requirements

- **OS**: Windows 10/11 (64-bit)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 500MB free space
- **Internet**: Required for AI features
- **Microphone**: Required for voice features

## 🆘 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Verify your API keys are correct
3. Ensure stable internet connection
4. Check Windows Event Viewer for error details

## 🎉 You're Ready!

Your Code Genius application is now ready to use. Simply:
1. Set up your `.env` file with API keys
2. Run `Code Genius.exe`
3. Start practicing for your technical interviews!

---

**Happy Coding and Good Luck with Your Interviews! 🚀**
