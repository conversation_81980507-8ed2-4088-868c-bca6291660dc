{"name": "code-genius", "version": "1.0.0", "main": "dist-electron/main.js", "author": "", "license": "ISC", "description": "A premium AI-powered coding assistant that helps you solve complex programming challenges.", "dependencies": {"@deepgram/sdk": "^4.1.1", "@electron/notarize": "^2.3.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@google/generative-ai": "^0.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.64.0", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "diff": "^7.0.0", "dotenv": "^16.4.7", "electron-log": "^5.3.4", "electron-store": "^10.0.1", "electron-updater": "^6.6.2", "form-data": "^4.0.1", "isomorphic-ws": "^5.0.0", "lucide-react": "^0.460.0", "react": "^18.2.0", "react-code-blocks": "^0.1.6", "react-dom": "^18.2.0", "react-router-dom": "^6.28.1", "react-syntax-highlighter": "^15.6.1", "screenshot-desktop": "^1.15.0", "tailwind-merge": "^2.5.5", "uuid": "^11.0.3", "ws-reconnect-js": "^1.0.4"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}