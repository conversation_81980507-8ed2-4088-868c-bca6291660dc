// ipcHandlers.ts

import { ipcMain, shell } from "electron"
import { randomBytes } from "crypto"
import { IIpcHandlerDeps } from "./main"

export function initializeIpcHandlers(deps: IIpcHandlerDeps): void {
  console.log("Initializing IPC handlers")

  // Credits handlers
  ipcMain.handle("set-initial-credits", async () => {
    const mainWindow = deps.getMainWindow()
    if (!mainWindow) {
      console.error("Main window not available for setting credits")
      return { success: false, error: "Main window not available" }
    }

    try {
      const credits = Number.MAX_SAFE_INTEGER
      await mainWindow.webContents.executeJavaScript(
        `window.__CREDITS__ = ${credits}`
      )
      mainWindow.webContents.send("credits-updated", credits)
      return { success: true, credits }
    } catch (error) {
      console.error("Error setting initial credits:", error)
      return { success: false, error: "Failed to set initial credits" }
    }
  })

  ipcMain.handle("decrement-credits", async () => {
    const mainWindow = deps.getMainWindow()
    if (!mainWindow) {
      console.error("Main window not available for decrementing credits")
      return { success: false, error: "Main window not available" }
    }

    try {
      // Get current credits first
      const currentCredits = await mainWindow.webContents.executeJavaScript(
        `window.__CREDITS__ || 0`
      )
      
      const newCredits = Math.max(0, currentCredits - 1)
      await mainWindow.webContents.executeJavaScript(
        `window.__CREDITS__ = ${newCredits}`
      )
      mainWindow.webContents.send("credits-updated", newCredits)
      return { success: true, credits: newCredits }
    } catch (error) {
      console.error("Error decrementing credits:", error)
      return { success: false, error: "Failed to decrement credits" }
    }
  })

  // Screenshot queue handlers
  ipcMain.handle("get-screenshot-queue", () => {
    try {
      return { success: true, queue: deps.getScreenshotQueue() }
    } catch (error) {
      console.error("Error getting screenshot queue:", error)
      return { success: false, error: "Failed to get screenshot queue" }
    }
  })

  ipcMain.handle("get-extra-screenshot-queue", () => {
    try {
      return { success: true, queue: deps.getExtraScreenshotQueue() }
    } catch (error) {
      console.error("Error getting extra screenshot queue:", error)
      return { success: false, error: "Failed to get extra screenshot queue" }
    }
  })

  ipcMain.handle("delete-screenshot", async (event, path: string) => {
    if (!path) {
      return { success: false, error: "Screenshot path is required" }
    }

    try {
      const result = await deps.deleteScreenshot(path)
      return { success: true, result }
    } catch (error) {
      console.error("Error deleting screenshot:", error)
      return { success: false, error: "Failed to delete screenshot" }
    }
  })

  ipcMain.handle("get-image-preview", async (event, path: string) => {
    if (!path) {
      return { success: false, error: "Image path is required" }
    }

    try {
      const preview = await deps.getImagePreview(path)
      return { success: true, preview }
    } catch (error) {
      console.error("Error getting image preview:", error)
      return { success: false, error: "Failed to get image preview" }
    }
  })

  // Screenshot processing handlers
  ipcMain.handle("process-screenshots", async () => {
    try {
      if (!deps.processingHelper) {
        return { success: false, error: "Processing helper not initialized" }
      }
      
      await deps.processingHelper.processScreenshots()
      return { success: true }
    } catch (error) {
      console.error("Error processing screenshots:", error)
      return { success: false, error: "Failed to process screenshots" }
    }
  })

  // Window dimension handlers
  ipcMain.handle(
    "update-content-dimensions",
    async (event, { width, height }: { width: number; height: number }) => {
      try {
        if (!width || !height || width <= 0 || height <= 0) {
          return { success: false, error: "Invalid dimensions provided" }
        }
        
        deps.setWindowDimensions(width, height)
        return { success: true }
      } catch (error) {
        console.error("Error updating content dimensions:", error)
        return { success: false, error: "Failed to update content dimensions" }
      }
    }
  )

  ipcMain.handle(
    "set-window-dimensions",
    (event, width: number, height: number) => {
      try {
        if (!width || !height || width <= 0 || height <= 0) {
          return { success: false, error: "Invalid dimensions provided" }
        }
        
        deps.setWindowDimensions(width, height)
        return { success: true }
      } catch (error) {
        console.error("Error setting window dimensions:", error)
        return { success: false, error: "Failed to set window dimensions" }
      }
    }
  )

  // Screenshot management handlers
  ipcMain.handle("get-screenshots", async () => {
    try {
      const currentView = deps.getView()
      const queue = currentView === "queue" 
        ? deps.getScreenshotQueue()
        : deps.getExtraScreenshotQueue()

      const previews = await Promise.all(
        queue.map(async (path) => {
          try {
            const preview = await deps.getImagePreview(path)
            return { path, preview, success: true }
          } catch (error) {
            console.error(`Error getting preview for ${path}:`, error)
            return { path, preview: null, success: false, error: error.message }
          }
        })
      )

      return { success: true, previews, view: currentView }
    } catch (error) {
      console.error("Error getting screenshots:", error)
      return { success: false, error: "Failed to get screenshots" }
    }
  })

  // Screenshot trigger handlers
  ipcMain.handle("trigger-screenshot", async () => {
    const mainWindow = deps.getMainWindow()
    if (!mainWindow) {
      return { success: false, error: "Main window not available" }
    }

    try {
      const screenshotPath = await deps.takeScreenshot()
      const preview = await deps.getImagePreview(screenshotPath)
      
      mainWindow.webContents.send("screenshot-taken", {
        path: screenshotPath,
        preview
      })
      
      return { success: true, path: screenshotPath }
    } catch (error) {
      console.error("Error triggering screenshot:", error)
      return { success: false, error: "Failed to trigger screenshot" }
    }
  })

  ipcMain.handle("take-screenshot", async () => {
    try {
      const screenshotPath = await deps.takeScreenshot()
      const preview = await deps.getImagePreview(screenshotPath)
      return { success: true, path: screenshotPath, preview }
    } catch (error) {
      console.error("Error taking screenshot:", error)
      return { success: false, error: "Failed to take screenshot" }
    }
  })

  // Auth related handlers
  ipcMain.handle("get-pkce-verifier", () => {
    try {
      const verifier = randomBytes(32).toString("base64url")
      return { success: true, verifier }
    } catch (error) {
      console.error("Error generating PKCE verifier:", error)
      return { success: false, error: "Failed to generate PKCE verifier" }
    }
  })

  ipcMain.handle("open-external-url", async (event, url: string) => {
    try {
      if (!url) {
        return { success: false, error: "URL is required" }
      }
      
      await shell.openExternal(url)
      return { success: true }
    } catch (error) {
      console.error("Error opening external URL:", error)
      return { success: false, error: "Failed to open external URL" }
    }
  })

  // Payment and License handlers
  ipcMain.handle("get-license-status", () => {
    try {
      if (!deps.paymentService) {
        return {
          success: false,
          licensed: false,
          trialActive: false,
          trialDaysLeft: 0,
          licenseKey: null,
          error: "Payment service not initialized"
        }
      }

      const status = deps.paymentService.getLicenseStatus()
      return { success: true, ...status }
    } catch (error) {
      console.error("Error getting license status:", error)
      return {
        success: false,
        licensed: false,
        trialActive: false,
        trialDaysLeft: 0,
        licenseKey: null,
        error: "Failed to get license status"
      }
    }
  })

  ipcMain.handle("activate-license", async (event, licenseKey: string) => {
    try {
      if (!licenseKey) {
        return { success: false, error: "License key is required" }
      }

      if (!deps.paymentService) {
        return { success: false, error: "Payment service not initialized" }
      }

      const success = deps.paymentService.activateLicense(licenseKey)
      return { success }
    } catch (error) {
      console.error("Error activating license:", error)
      return { success: false, error: "Failed to activate license" }
    }
  })

  ipcMain.handle("check-license", () => {
    try {
      if (!deps.paymentService) {
        return { success: false, licensed: false, error: "Payment service not initialized" }
      }

      const licensed = deps.paymentService.isLicensed()
      return { success: true, licensed }
    } catch (error) {
      console.error("Error checking license:", error)
      return { success: false, licensed: false, error: "Failed to check license" }
    }
  })

  // Subscription handlers
  ipcMain.handle("open-settings-portal", () => {
    try {
      const mainWindow = deps.getMainWindow()
      if (!mainWindow) {
        return { success: false, error: "Main window not available" }
      }

      mainWindow.webContents.send("show-license-dialog")
      return { success: true }
    } catch (error) {
      console.error("Error opening settings portal:", error)
      return { success: false, error: "Failed to open settings portal" }
    }
  })

  ipcMain.handle("open-subscription-portal", () => {
    try {
      const mainWindow = deps.getMainWindow()
      if (!mainWindow) {
        return { success: false, error: "Main window not available" }
      }

      mainWindow.webContents.send("show-license-dialog")
      return { success: true }
    } catch (error) {
      console.error("Error opening subscription portal:", error)
      return { success: false, error: "Failed to open subscription portal" }
    }
  })

  // Window management handlers
  ipcMain.handle("toggle-window", () => {
    try {
      deps.toggleMainWindow()
      return { success: true }
    } catch (error) {
      console.error("Error toggling window:", error)
      return { success: false, error: "Failed to toggle window" }
    }
  })

  ipcMain.handle("reset-queues", async () => {
    try {
      deps.clearQueues()
      return { success: true }
    } catch (error) {
      console.error("Error resetting queues:", error)
      return { success: false, error: "Failed to reset queues" }
    }
  })

  // Process screenshot handlers
  ipcMain.handle("trigger-process-screenshots", async () => {
    try {
      if (!deps.processingHelper) {
        return { success: false, error: "Processing helper not initialized" }
      }

      await deps.processingHelper.processScreenshots()
      return { success: true }
    } catch (error) {
      console.error("Error processing screenshots:", error)
      return { success: false, error: "Failed to process screenshots" }
    }
  })

  // Process extra screenshots for debugging
  ipcMain.handle("trigger-process-extra-screenshots", async () => {
    try {
      if (!deps.processingHelper) {
        return { success: false, error: "Processing helper not initialized" }
      }

      await deps.processingHelper.processExtraScreenshots()
      return { success: true }
    } catch (error) {
      console.error("Error processing extra screenshots:", error)
      return { success: false, error: "Failed to debug solution" }
    }
  })

  // Reset handlers
  ipcMain.handle("trigger-reset", () => {
    try {
      // Cancel ongoing requests
      deps.processingHelper?.cancelOngoingRequests()

      // Clear all queues
      deps.clearQueues()

      // Reset view to queue
      deps.setView("queue")

      // Send reset events to renderer
      const mainWindow = deps.getMainWindow()
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send("reset-view")
        mainWindow.webContents.send("reset")
      }

      return { success: true }
    } catch (error) {
      console.error("Error triggering reset:", error)
      return { success: false, error: "Failed to trigger reset" }
    }
  })

  // Window movement handlers
  ipcMain.handle("trigger-move-left", () => {
    try {
      deps.moveWindowLeft()
      return { success: true }
    } catch (error) {
      console.error("Error moving window left:", error)
      return { success: false, error: "Failed to move window left" }
    }
  })

  ipcMain.handle("trigger-move-right", () => {
    try {
      deps.moveWindowRight()
      return { success: true }
    } catch (error) {
      console.error("Error moving window right:", error)
      return { success: false, error: "Failed to move window right" }
    }
  })

  ipcMain.handle("trigger-move-up", () => {
    try {
      deps.moveWindowUp()
      return { success: true }
    } catch (error) {
      console.error("Error moving window up:", error)
      return { success: false, error: "Failed to move window up" }
    }
  })

  ipcMain.handle("trigger-move-down", () => {
    try {
      deps.moveWindowDown()
      return { success: true }
    } catch (error) {
      console.error("Error moving window down:", error)
      return { success: false, error: "Failed to move window down" }
    }
  })

  // Auto system SST handlers
  ipcMain.handle("set-auto-system-sst", (event, enabled: boolean) => {
    try {
      console.log('Setting auto system SST to:', enabled)

      if (!deps.geminiLiveService) {
        return { success: false, error: "Gemini Live Service not initialized" }
      }

      deps.geminiLiveService.setAutoSystemSST(enabled)
      const actualEnabled = deps.geminiLiveService.getAutoSystemSSTEnabled()

      // Notify renderer
      deps.getMainWindow()?.webContents.send('auto-system-sst-status', {
        enabled: actualEnabled
      })

      return { success: true, enabled: actualEnabled }
    } catch (error) {
      console.error("Error setting auto system SST:", error)
      return { success: false, error: "Failed to set auto system SST" }
    }
  })

  ipcMain.handle("get-auto-system-sst", () => {
    try {
      if (!deps.geminiLiveService) {
        return { success: false, error: "Gemini Live Service not initialized" }
      }

      const enabled = deps.geminiLiveService.getAutoSystemSSTEnabled()
      return { success: true, enabled }
    } catch (error) {
      console.error("Error getting auto system SST:", error)
      return { success: false, error: "Failed to get auto system SST" }
    }
  })

  // Autonomous mode handlers
  ipcMain.handle("set-autonomous-mode", (event, enabled: boolean) => {
    try {
      console.log('Setting autonomous mode to:', enabled)

      if (!deps.geminiLiveService) {
        return { success: false, error: "Gemini Live Service not initialized" }
      }

      deps.geminiLiveService.setAutonomousMode(enabled)
      const actualEnabled = deps.geminiLiveService.getAutonomousMode()

      // Notify renderer
      deps.getMainWindow()?.webContents.send('autonomous-mode-status', {
        enabled: actualEnabled
      })

      return { success: true, enabled: actualEnabled }
    } catch (error) {
      console.error("Error setting autonomous mode:", error)
      return { success: false, error: "Failed to set autonomous mode" }
    }
  })

  ipcMain.handle("get-autonomous-mode", () => {
    try {
      if (!deps.geminiLiveService) {
        return { success: false, error: "Gemini Live Service not initialized" }
      }

      const enabled = deps.geminiLiveService.getAutonomousMode()
      return { success: true, enabled }
    } catch (error) {
      console.error("Error getting autonomous mode:", error)
      return { success: false, error: "Failed to get autonomous mode status" }
    }
  })

  // Voice processing handler
  ipcMain.handle("process-voice-input", async (
    event,
    transcript: string,
    language: string,
    audioBuffer?: Uint8Array,
    isUserSpeech: boolean = true,
    resumeInfo?: string,
    jobDescription?: string
  ) => {
    try {
      console.log('Processing voice input:', {
        transcript: transcript?.substring(0, 100),
        language,
        hasAudioBuffer: !!audioBuffer,
        audioBufferSize: audioBuffer?.length,
        isUserSpeech,
        hasResumeInfo: !!resumeInfo,
        hasJobDescription: !!jobDescription
      })

      // Debug audio buffer
      if (audioBuffer) {
        console.log('Audio buffer details:', {
          length: audioBuffer.length,
          type: typeof audioBuffer,
          constructor: audioBuffer.constructor.name,
          firstFewBytes: Array.from(audioBuffer.slice(0, 20)).map(b => b.toString(16).padStart(2, '0')).join(' ')
        });
      }

      if (!transcript && !audioBuffer) {
        return {
          success: false,
          error: "Either transcript or audio buffer is required",
          transcript: ""
        }
      }

      let recognizedTranscript = transcript || ""
      let response: string

      // Try GeminiLiveService first
      if (deps.geminiLiveService?.isReady()) {
        try {
          console.log('Using GeminiLiveService for voice processing')
          
          // Initialize session if needed
          if (!deps.geminiLiveService.isConnected()) {
            const initialized = await deps.geminiLiveService.initSession()
            if (!initialized) {
              throw new Error('Failed to initialize GeminiLiveService session')
            }
          }

          // Convert audioBuffer to Buffer with debugging
          let bufferForProcessing: Buffer | undefined = undefined;
          if (audioBuffer) {
            bufferForProcessing = Buffer.from(audioBuffer);
            console.log('Converted to Buffer:', {
              originalLength: audioBuffer.length,
              bufferLength: bufferForProcessing.length,
              firstFewBytes: Array.from(bufferForProcessing.subarray(0, 20)).map(b => b.toString(16).padStart(2, '0')).join(' ')
            });
          }

          const result = await deps.geminiLiveService!.processVoiceInput(
            transcript,
            language,
            bufferForProcessing,
            isUserSpeech,
            resumeInfo,
            jobDescription
          )

          if (result && typeof result === 'object' && 'response' in result) {
            response = String(result.response)
            recognizedTranscript = String(result.transcript || transcript)
          } else {
            response = String(result || '')
          }

          return {
            success: true,
            response,
            transcript: recognizedTranscript
          }
        } catch (liveError) {
          console.error('GeminiLiveService error:', liveError)
          
          // Send warning to renderer
          deps.getMainWindow()?.webContents.send('voice-processing-warning', {
            warning: 'Live voice processing failed, falling back to standard processing',
            error: liveError.message,
            transcript: recognizedTranscript
          })

          // Continue to fallback
        }
      }

      // Fallback to ProcessingHelper
      if (!deps.processingHelper) {
        const error = "Processing helper not initialized"
        deps.getMainWindow()?.webContents.send('voice-processing-error', {
          error,
          transcript: recognizedTranscript
        })
        return { success: false, error, transcript: recognizedTranscript }
      }

      console.log('Using ProcessingHelper for voice processing')
      response = await deps.processingHelper.processVoiceInput(transcript, language)

      return {
        success: true,
        response,
        transcript: recognizedTranscript
      }
    } catch (error) {
      console.error("Error processing voice input:", error)

      // Send error to renderer
      deps.getMainWindow()?.webContents.send('voice-processing-error', {
        error: 'Voice processing failed',
        details: error.message,
        transcript: transcript || ""
      })

      return {
        success: false,
        error: `Failed to process voice input: ${error.message}`,
        transcript: transcript || ""
      }
    }
  })

  console.log("IPC handlers initialization complete")
}